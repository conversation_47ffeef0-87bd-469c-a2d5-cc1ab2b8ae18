package com.tqhit.battery.one.features.stats.health.repository

import android.util.Log
import com.tqhit.battery.one.features.stats.corebattery.domain.CoreBatteryStatsProvider
import com.tqhit.battery.one.features.stats.health.cache.HealthCache
import com.tqhit.battery.one.features.stats.health.data.HealthCalculationMode
import com.tqhit.battery.one.features.stats.health.data.HealthChartData
import com.tqhit.battery.one.features.stats.health.data.HealthStatus
import com.tqhit.battery.one.manager.charge.ChargingSessionManager
import com.tqhit.battery.one.repository.AppRepository
import com.tqhit.battery.one.viewmodel.battery.BatteryViewModel
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Interface for the HealthRepository.
 * Provides reactive streams for health status and chart data.
 */
interface HealthRepository {
    
    /**
     * Flow that emits the latest HealthStatus.
     */
    val healthStatusFlow: Flow<HealthStatus>
    
    /**
     * Flow that emits the latest HealthChartData.
     */
    val healthChartDataFlow: Flow<HealthChartData>
    
    /**
     * Gets the current health calculation mode.
     * 
     * @return Current HealthCalculationMode
     */
    suspend fun getCurrentCalculationMode(): HealthCalculationMode
    
    /**
     * Switches the health calculation mode and recalculates health status.
     * 
     * @param mode The new calculation mode to use
     */
    suspend fun switchCalculationMode(mode: HealthCalculationMode)
    
    /**
     * Updates the chart data for a specific time range.
     * 
     * @param timeRangeHours The time range in hours (4, 8, 12, 24)
     */
    suspend fun updateChartData(timeRangeHours: Int)
    
    /**
     * Generates sample charging sessions if none exist.
     * This is used for demonstration purposes as specified in the PRD.
     */
    suspend fun generateSampleSessionsIfEmpty()
    
    /**
     * Gets the current health status synchronously.
     * 
     * @return Current HealthStatus or null if not available
     */
    fun getCurrentHealthStatus(): HealthStatus?
    
    /**
     * Gets the current chart data synchronously.
     * 
     * @return Current HealthChartData or null if not available
     */
    fun getCurrentChartData(): HealthChartData?
}

/**
 * Default implementation of HealthRepository.
 * Integrates with CoreBatteryStatsProvider and manages health calculations.
 */
@Singleton
class DefaultHealthRepository @Inject constructor(
    private val coreBatteryStatsProvider: CoreBatteryStatsProvider,
    private val chargingSessionManager: ChargingSessionManager,
    private val appRepository: AppRepository,
    private val healthCache: HealthCache,
    private val batteryViewModel: BatteryViewModel // Temporary for chart data during migration
) : HealthRepository {
    
    companion object {
        private const val TAG = "HealthRepository"
        private const val DEFAULT_TIME_RANGE_HOURS = 4
    }
    
    // Coroutine scope for repository operations
    private val repositoryScope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    
    // Private mutable flows for internal updates
    private val _healthStatusFlow = MutableStateFlow(HealthStatus.createDefault())
    private val _healthChartDataFlow = MutableStateFlow(HealthChartData.createEmpty())
    
    // Public read-only flows for external consumption
    override val healthStatusFlow: StateFlow<HealthStatus> = _healthStatusFlow.asStateFlow()
    override val healthChartDataFlow: StateFlow<HealthChartData> = _healthChartDataFlow.asStateFlow()
    
    // Current calculation mode
    private var currentCalculationMode = HealthCalculationMode.CUMULATIVE
    
    init {
        // Load calculation mode from cache on initialization
        repositoryScope.launch {
            loadCalculationModeFromCache()
            generateSampleSessionsIfEmpty()
            updateHealthStatus()
            updateChartData(DEFAULT_TIME_RANGE_HOURS)
        }
        
        // Start collecting CoreBatteryStatus updates for real-time health updates
        repositoryScope.launch {
            coreBatteryStatsProvider.coreBatteryStatusFlow.collect { coreStatus ->
                coreStatus?.let { 
                    updateHealthStatus()
                    Log.v(TAG, "HEALTH_REPO: Health status updated due to core battery status change")
                }
            }
        }
    }
    
    /**
     * Loads the calculation mode from cache.
     */
    private suspend fun loadCalculationModeFromCache() {
        try {
            currentCalculationMode = healthCache.getCalculationMode()
            Log.d(TAG, "HEALTH_REPO: Calculation mode loaded from cache: $currentCalculationMode")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to load calculation mode from cache, using default", e)
            currentCalculationMode = HealthCalculationMode.CUMULATIVE
        }
    }
    
    /**
     * Updates the health status based on current data.
     */
    private suspend fun updateHealthStatus() {
        try {
            val totalSessions = chargingSessionManager.getTotalSessions()
            val designCapacity = appRepository.getBatteryCapacity()
            
            val newHealthStatus = HealthStatus.createCalculated(
                totalSessions = totalSessions,
                designCapacityMah = designCapacity,
                calculationMode = currentCalculationMode
            )
            
            _healthStatusFlow.value = newHealthStatus
            
            // Cache the updated status
            healthCache.saveHealthStatus(newHealthStatus)
            
            Log.d(TAG, "HEALTH_REPO: Health status updated - " +
                "health=${newHealthStatus.healthPercentage}%, " +
                "sessions=$totalSessions, " +
                "mode=$currentCalculationMode")
                
        } catch (e: Exception) {
            Log.e(TAG, "Failed to update health status", e)
            
            // Emit default status on error
            val defaultStatus = HealthStatus.createDefault(appRepository.getBatteryCapacity())
            _healthStatusFlow.value = defaultStatus
        }
    }
    
    override suspend fun getCurrentCalculationMode(): HealthCalculationMode {
        return currentCalculationMode
    }
    
    override suspend fun switchCalculationMode(mode: HealthCalculationMode) {
        try {
            currentCalculationMode = mode
            
            // Save to cache
            healthCache.saveCalculationMode(mode)
            
            // Recalculate health status with new mode
            updateHealthStatus()
            
            Log.d(TAG, "HEALTH_REPO: Calculation mode switched to: $mode")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to switch calculation mode", e)
        }
    }
    
    override suspend fun updateChartData(timeRangeHours: Int) {
        try {
            // For now, use BatteryViewModel for chart data (legacy during migration)
            // TODO: Replace with health-specific data processing
            val batteryHistory = batteryViewModel.getHistoryBatteryForHours(timeRangeHours)
            val temperatureHistory = batteryViewModel.getHistoryBatteryForHours(timeRangeHours) // Placeholder
            val dailyWearData = batteryViewModel.getDailyWearData(7)
            
            // Convert to chart entries (simplified for now)
            val batteryEntries = batteryHistory.mapIndexed { index, value ->
                com.github.mikephil.charting.data.Entry(index.toFloat(), value.toFloat())
            }
            
            val temperatureEntries = temperatureHistory.mapIndexed { index, value ->
                com.github.mikephil.charting.data.Entry(index.toFloat(), (value + 20).toFloat()) // Mock temperature
            }
            
            val newChartData = HealthChartData(
                batteryPercentageEntries = batteryEntries,
                temperatureEntries = temperatureEntries,
                dailyWearData = dailyWearData,
                selectedTimeRangeHours = timeRangeHours
            )
            
            _healthChartDataFlow.value = newChartData
            
            Log.d(TAG, "HEALTH_REPO: Chart data updated for ${timeRangeHours}h range - " +
                "batteryPoints=${batteryEntries.size}, " +
                "tempPoints=${temperatureEntries.size}")
                
        } catch (e: Exception) {
            Log.e(TAG, "Failed to update chart data", e)
            
            // Emit sample data on error
            val sampleData = HealthChartData.createSample(timeRangeHours)
            _healthChartDataFlow.value = sampleData
        }
    }
    
    override suspend fun generateSampleSessionsIfEmpty() {
        try {
            val currentSessions = chargingSessionManager.getTotalSessions()
            if (currentSessions == 0) {
                // Generate 10 sample sessions as specified in PRD
                chargingSessionManager.addSampleSessionsIfEmpty()
                Log.d(TAG, "HEALTH_REPO: Sample sessions generated for demonstration")
            } else {
                Log.d(TAG, "HEALTH_REPO: Existing sessions found ($currentSessions), no sample generation needed")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Failed to generate sample sessions", e)
        }
    }
    
    override fun getCurrentHealthStatus(): HealthStatus? {
        return _healthStatusFlow.value
    }
    
    override fun getCurrentChartData(): HealthChartData? {
        return _healthChartDataFlow.value
    }
}
